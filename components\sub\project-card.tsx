import { motion } from "framer-motion";
import Image from "next/image";
import { GlowingEffect } from "@/components/ui/glowing-effect";

type ProjectCardProps = {
  src: string;
  title: string;
  description: string;
  link?: string; // Optional since we're not using it directly
  isLink?: boolean;
  category?: string;
};

export const ProjectCard = ({
  src,
  title,
  description,
  link: _link, // Renamed to avoid unused variable warning
  isLink = false,
  category,
}: ProjectCardProps) => {
  // Animation variants
  const imageVariants = {
    hover: {
      scale: 1.1,
      transition: { duration: 0.5, ease: [0.25, 0.1, 0.25, 1] }
    }
  };

  const arrowVariants = {
    hover: {
      x: 5,
      transition: {
        repeat: Infinity,
        repeatType: "mirror" as const,
        duration: 0.8,
        ease: "easeInOut"
      }
    }
  };

  const CardContent = () => (
    <motion.div
      className="h-full flex flex-col"
      initial="initial"
      whileHover="hover"
    >
      <div className="relative h-[200px] overflow-hidden">
        <motion.div
          variants={imageVariants}
          className="h-full w-full"
        >
          <Image
            src={src}
            alt={title}
            fill
            className="object-cover"
          />
        </motion.div>
        {category && (
          <motion.div
            className="absolute top-4 right-4 bg-purple-600 text-white text-xs px-2 py-1 rounded-full"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 0 10px rgba(124, 58, 237, 0.5)"
            }}
          >
            {category}
          </motion.div>
        )}
      </div>

      <div className="relative p-6 flex flex-col flex-grow">
        <h1 className="text-2xl font-semibold text-white mb-2">{title}</h1>
        <p className="text-gray-300 flex-grow">{description}</p>
        <div className="mt-4 flex justify-end">
          <motion.span className="text-purple-400 text-sm flex items-center">
            {isLink ? "View Details" : "Visit Project"}
            <motion.svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              variants={arrowVariants}
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </motion.svg>
          </motion.span>
        </div>
      </div>
    </motion.div>
  );

  const cardClasses = "relative overflow-hidden rounded-lg shadow-lg border border-[#2A0E61] bg-[rgba(3,0,20,0.5)] hover:border-purple-500 hover:shadow-purple-500/20 hover:shadow-lg transition-all duration-300 h-full will-change-transform";

  return (
    <div className={cardClasses}>
      <GlowingEffect
        spread={80}
        glow={true}
        disabled={false}
        proximity={120}
        inactiveZone={0.2}
        blur={3}
        borderWidth={3}
      />
      <CardContent />
    </div>
  );
};
