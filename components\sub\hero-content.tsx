"use client";

import { SparklesIcon } from "@heroicons/react/24/solid";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import { useRef } from "react";

import {
  fadeIn,
  scaleUp,
  slideInFromRight,
  slideInFromTop,
  staggerContainer
} from "@/lib/motion";
import { Cover } from "@/components/ui/cover";

export const HeroContent = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Parallax scroll effect
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const imageY = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const textY = useTransform(scrollYProgress, [0, 1], [0, 50]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  // Text animation variants

  const titleWord = {
    hidden: {
      opacity: 0,
      y: 20,
      filter: "blur(10px)"
    },
    visible: {
      opacity: 1,
      y: 0,
      filter: "blur(0px)",
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      }
    }
  };

  // Split text for word-by-word animation
  const titleWords = ["Transforming", "ideas", "into", "digital", "reality."];

  return (
    <motion.div
      ref={containerRef}
      initial="hidden"
      animate="visible"
      className="flex flex-col md:flex-row items-center justify-center px-4 sm:px-8 md:px-12 lg:px-20 mt-20 md:mt-32 lg:mt-40 w-full z-[20]"
      style={{ opacity }}
    >
      <motion.div
        className="h-full w-full flex flex-col gap-4 md:gap-5 justify-center m-auto text-center md:text-start"
        style={{ y: textY }}
      >
        <motion.div
          variants={slideInFromTop}
          className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mx-auto md:mx-0 animate-float will-change-transform"
        >
          <SparklesIcon className="text-[#b49bff] mr-[10px] h-5 w-5" />
          <h1 className="Welcome-text text-[13px]">
            Innovative Digital Solutions
          </h1>
        </motion.div>

        <motion.div
          variants={staggerContainer(0.1, 0.3)}
          className="flex flex-col gap-4 md:gap-6 mt-4 md:mt-6 text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white max-w-[600px] w-auto h-auto"
        >
          <div className="flex flex-wrap justify-center md:justify-start">
            {titleWords.map((word) => (
              <motion.span
                key={`word-${word}`}
                className="mr-3"
                variants={titleWord}
              >
                {word === "digital" || word === "reality." ? (
                  <Cover className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">
                    {word}
                  </Cover>
                ) : (
                  <span className={word === "ideas" ? "text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500" : ""}>
                    {word}
                  </span>
                )}
              </motion.span>
            ))}
          </div>
        </motion.div>

        <motion.p
          variants={fadeIn(0.8, 0.7)}
          className="text-base md:text-lg text-gray-400 my-3 md:my-5 max-w-[600px]"
        >
          Oyu Intelligence LLC specializes in AI Automation, Mobile App Development,
          Website Design & Development, and Social Media Development. We turn your vision into reality.
        </motion.p>

        <motion.a
          variants={scaleUp(1)}
          href="#services"
          className="py-2 button-primary text-center text-white cursor-pointer rounded-lg max-w-[200px] mx-auto md:mx-0"
          whileHover={{
            scale: 1.05,
            boxShadow: "0 0 20px rgba(124, 58, 237, 0.5)"
          }}
          whileTap={{ scale: 0.95 }}
        >
          Our Services
        </motion.a>
      </motion.div>

      <motion.div
        variants={slideInFromRight(0.8)}
        className="w-full h-full flex justify-center items-center mt-8 md:mt-0 will-change-transform"
        style={{ y: imageY }}
      >
        {/* Animated glow effect */}
        <motion.div
          className="absolute w-full max-w-[500px] h-[500px] bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1, 0.95, 1],
            opacity: [0.5, 0.7, 0.5, 0.3, 0.5]
          }}
          transition={{ duration: 8, repeat: Infinity }}
        />

        <motion.div
          className="animate-float will-change-transform"
        >
          <Image
            src="/hero-bg.svg"
            alt="work icons"
            height={650}
            width={650}
            draggable={false}
            className="select-none w-full max-w-[300px] sm:max-w-[400px] md:max-w-[500px] lg:max-w-[650px]"
          />
        </motion.div>
      </motion.div>
    </motion.div>
  );
};
