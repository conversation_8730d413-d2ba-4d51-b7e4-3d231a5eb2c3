"use client";

import { motion, useInView } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";

import { slideInFromLeft, slideInFromRight, slideInFromTop } from "@/lib/motion";
import { BlogPostType } from "@/types";
import { GlowingEffect } from "@/components/ui/glowing-effect";

// Blog card component
const BlogCard = ({
  post,
  index
}: {
  post: BlogPostType;
  index: number;
}) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true });

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/blog/${post.slug}`} className="block">
        <div className="relative bg-[rgba(15,5,30,0.4)] rounded-xl border border-purple-500/20 overflow-hidden group-hover:border-purple-500 transition-all duration-300 h-full flex flex-col shadow-lg shadow-purple-900/10 hover:shadow-purple-800/20 hover:-translate-y-1">
          <GlowingEffect
            spread={40}
            glow={true}
            disabled={false}
            proximity={64}
            inactiveZone={0.01}
          />
          {/* Image */}
          <div className="relative h-52 w-full overflow-hidden">
            <Image
              src={post.image}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-[rgba(15,5,30,0.8)] to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300"></div>
            <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-600 to-purple-500 text-white text-xs px-3 py-1.5 rounded-full shadow-md">
              {post.category}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 flex flex-col flex-grow">
            <div className="flex items-center mb-4">
              <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-purple-500/30">
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-sm">
                <span className="text-white font-medium">{post.author}</span>
                <div className="flex items-center text-gray-400">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-2">
              {post.title}
            </h3>

            <p className="text-gray-300 text-sm mb-5 flex-grow line-clamp-3">
              {post.excerpt}
            </p>

            <div className="mt-auto">
              <span className="text-purple-400 text-sm font-medium flex items-center group-hover:text-purple-300 transition-colors">
                Read article
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export const BlogPreview = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.2 });

  const blogPosts: BlogPostType[] = [
    {
      id: 1,
      slug: "ai-automation-trends-2024",
      title: "Top AI Automation Trends to Watch in 2024",
      excerpt: "Discover the emerging AI automation trends that are transforming businesses and creating new opportunities for growth and innovation.",
      content: "",
      image: "/blog/ai-trends.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person1.jpg",
      date: "Jan 15, 2024",
      category: "AI Automation",
      readTime: "5 min read"
    },
    {
      id: 2,
      slug: "mobile-app-development-best-practices",
      title: "Best Practices for Successful Mobile App Development",
      excerpt: "Learn the essential best practices that can help ensure your mobile app development project succeeds in today's competitive market.",
      content: "",
      image: "/blog/mobile-app.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person2.jpg",
      date: "Feb 3, 2024",
      category: "Mobile App",
      readTime: "7 min read"
    },
    {
      id: 3,
      slug: "web-design-trends-2024",
      title: "Web Design Trends That Will Dominate in 2024",
      excerpt: "Stay ahead of the curve with these cutting-edge web design trends that are set to define the digital landscape in 2024.",
      content: "",
      image: "/blog/web-design.jpg",
      author: "Boldbat Khuukhenduu",
      authorImage: "/testimonials/person3.jpg",
      date: "Feb 18, 2024",
      category: "Web Development",
      readTime: "6 min read"
    },
  ];

  return (
    <section
      id="blog"
      ref={sectionRef}
      className="relative flex flex-col items-center justify-center py-20 px-4 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-purple-900/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-cyan-900/20 rounded-full blur-3xl"></div>

      <motion.div
        variants={slideInFromTop}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="Welcome-box py-[8px] px-[7px] border border-[#7042f88b] opacity-[0.9]] mb-6"
      >
        <h1 className="Welcome-text text-[13px]">
          Latest Insights
        </h1>
      </motion.div>

      <motion.h2
        variants={slideInFromLeft(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-4xl font-bold text-white mb-4 text-center"
      >
        Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-cyan-500">Blog</span>
      </motion.h2>

      <motion.p
        variants={slideInFromRight(0.5)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="text-gray-400 text-center max-w-2xl mb-16"
      >
        Stay updated with the latest trends, insights, and best practices in technology and digital innovation.
      </motion.p>

      {/* Blog posts grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl">
        {blogPosts.map((post, index) => (
          <BlogCard
            key={post.id}
            post={post}
            index={index}
          />
        ))}
      </div>

      {/* View all button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-16"
      >
        <Link
          href="/blog"
          className="px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-lg hover:opacity-90 transition-all duration-300 flex items-center font-medium shadow-lg shadow-purple-900/20 hover:shadow-purple-800/30 hover:-translate-y-1"
        >
          <span>View All Articles</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </Link>
      </motion.div>
    </section>
  );
};
